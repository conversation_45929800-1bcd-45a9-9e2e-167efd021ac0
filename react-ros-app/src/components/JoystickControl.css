.joystick-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 10px;
  margin: 10px;
}

.joystick {
  background-color: #e0e0e0;
  border-radius: 50%;
  margin: 20px;
  position: relative;
}

.joystick-instructions {
  margin-top: 20px;
  text-align: center;
}

.joystick-instructions p {
  margin: 5px 0;
  font-size: 14px;
}

/* Speed Controls Styling */
.speed-controls {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.speed-control-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
}

.speed-control-group h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #333;
}

.speed-display {
  font-size: 18px;
  font-weight: bold;
  color: #2196F3;
  margin-bottom: 10px;
  padding: 5px 10px;
  background-color: #f0f8ff;
  border-radius: 4px;
  min-width: 80px;
  text-align: center;
}

.speed-buttons {
  display: flex;
  gap: 10px;
}

.speed-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.speed-btn.decrease {
  background-color: #ff6b6b;
  color: white;
}

.speed-btn.increase {
  background-color: #4ecdc4;
  color: white;
}

.speed-btn:hover:not(:disabled) {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.speed-btn:disabled {
  background-color: #cccccc;
  color: #666666;
  cursor: not-allowed;
  transform: none;
}

.speed-btn:active:not(:disabled) {
  transform: scale(0.95);
}

<launch>
    <!-- This launch file runs the full simulation without RVIZ on Device 1 (robot) -->
    
    <!-- Launch Gazebo -->
    <include file="$(find weednix_launch)/launch/gazebo_simulation.launch" />

    <!-- Launch EKF -->
    <node pkg="robot_localization" type="ekf_localization_node" name="ekf_filter_node" output="screen">
        <rosparam command="load" file="$(find weednix_launch)/config/ekf_simualtion.yaml" />
    </node>

    <!-- Launch row crop follower -->

  
    <!-- Launch path publisher -->
    <node name="path_publisher" pkg="visual_servoing" type="path_publisher" output="screen" />

    <!-- Note: RVIZ is intentionally not included as it will run on Device 2 -->
</launch>
